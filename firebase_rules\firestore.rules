rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // ===== 사용자 기본 문서 =====
    match /users/{userId} {
      // 본인 데이터만 읽기/쓰기 가능
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // 닉네임 중복 확인을 위한 제한적 읽기 허용
      // 인증된 사용자만 닉네임 필드 읽기 가능
      allow read: if request.auth != null;

      // 전화번호 업데이트 시 추가 보안 검증
      allow update: if request.auth != null
        && request.auth.uid == userId
        && (
          // 전화번호 필드가 변경되지 않거나
          !('phone' in request.resource.data) ||
          // 전화번호 형식이 올바른 경우만 허용
          (request.resource.data.phone is string &&
           request.resource.data.phone.matches('^01[0-9]-[0-9]{4}-[0-9]{4}$'))
        );



      // ===== 사용자별 기타 하위 컬렉션 (개별 정의) =====
      // 필요한 컬렉션들을 개별적으로 정의 (본인 데이터만)
      match /categories/{categoryId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /products/{productId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /sellers/{sellerId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /sales/{saleId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /prepayments/{prepaymentId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // ===== 구독 관리 =====
      match /subscriptions/{subscriptionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /payments/{paymentId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /cards/{cardId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // ===== 사용자 설정 =====
      match /settings/{settingId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // ===== SMS 인증 (전화번호를 키로 사용) =====
    match /sms_verifications/{phoneNumber} {
      // 인증 요청한 사용자만 접근 가능
      allow read, write: if request.auth != null &&
        resource.data.uid == request.auth.uid;

      // 새 인증 요청 생성 (Functions에서만)
      allow create: if false; // Functions 전용
    }

    // ===== 핸드폰 번호 중복 방지 (새로 추가) =====
    match /phone_numbers/{phoneNumber} {
      // 해당 번호를 소유한 사용자만 읽기 가능
      allow read: if request.auth != null &&
        resource.data.userId == request.auth.uid;

      // 생성/업데이트는 Functions에서만
      allow write: if false; // Functions 전용
    }

    // ===== 구독 바인딩 (구독-계정 연결 관리) =====
    match /subscription_bindings/{bindingId} {
      // 인증된 사용자만 접근 가능
      allow read: if request.auth != null && (
        // 기존 바인딩: 자신의 바인딩만 읽기 가능
        (resource != null && resource.data.boundUserId == request.auth.uid) ||
        // 새 바인딩 확인: 모든 인증된 사용자가 바인딩 존재 여부 확인 가능
        resource == null
      );

      // 바인딩 생성: 인증된 사용자가 자신을 boundUserId로 하는 바인딩만 생성 가능
      allow create: if request.auth != null &&
        request.resource.data.boundUserId == request.auth.uid &&
        request.resource.data.keys().hasAll(['boundUserId', 'platform', 'createdAt', 'productId']);

      // 바인딩 업데이트: 자신의 바인딩만 업데이트 가능
      allow update: if request.auth != null &&
        resource.data.boundUserId == request.auth.uid &&
        request.resource.data.boundUserId == request.auth.uid;

      // 바인딩 삭제: 자신의 바인딩만 삭제 가능
      allow delete: if request.auth != null &&
        resource.data.boundUserId == request.auth.uid;
    }









    // ===== 관리자 전용 =====
    match /admin/{document=**} {
      allow read, write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // ===== 공개 데이터 =====
    match /public/{document=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // ===== 사용량 통계 =====
    match /usage_stats/{document=**} {
      allow read: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
      allow write: if false; // Functions 전용
    }

    // ===== 기본 거부 =====
    match /{document=**} {
      allow read, write: if false;
    }
  }
}